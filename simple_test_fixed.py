#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修复后的搜索功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    try:
        from email_page.spider import GoogleSearch
        print("✅ 成功导入GoogleSearch")
        
        # 创建搜索实例
        searcher = GoogleSearch(thread_id=999)
        print("✅ 成功创建GoogleSearch实例")
        
        # 测试一个简单的搜索
        keyword = "python"
        print(f"🔍 测试搜索关键词: {keyword}")
        
        result = searcher.search(keyword)
        print(f"📊 搜索结果: {result}")
        
        if result is not None:
            print("✅ 搜索功能正常工作")
        else:
            print("⚠️ 搜索返回None，可能需要进一步调试")
        
        # 检查是否生成了调试HTML文件
        debug_dir = "debug_html"
        if os.path.exists(debug_dir):
            files = os.listdir(debug_dir)
            html_files = [f for f in files if f.endswith('.html')]
            print(f"📁 生成了 {len(html_files)} 个HTML调试文件")
            
            if html_files:
                latest_file = html_files[-1]
                file_path = os.path.join(debug_dir, latest_file)
                file_size = os.path.getsize(file_path)
                print(f"📄 最新HTML文件: {latest_file} ({file_size} 字节)")
                
                # 简单检查HTML内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if len(content) > 10000:
                    print("✅ HTML内容充足")
                else:
                    print("⚠️ HTML内容可能不完整")
                
                if 'google' in content.lower():
                    print("✅ HTML包含Google标识")
                else:
                    print("❌ HTML不包含Google标识")
                
                if 'NKTSme' in content:
                    print("✅ HTML包含分页元素")
                else:
                    print("❌ HTML不包含分页元素")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("修复后的Google搜索功能测试")
    print("=" * 50)
    
    success = test_basic_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 基本功能测试通过！")
        print("现在可以运行完整的HTML质量测试")
    else:
        print("🚨 基本功能测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
