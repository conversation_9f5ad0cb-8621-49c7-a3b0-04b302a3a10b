#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie有效性的脚本
"""

import sys
import os
import requests
from lxml import etree
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_page.cookies import GoogleCookie

def test_cookie_validity():
    """测试当前Cookie的有效性"""
    print("=== 测试Cookie有效性 ===")
    
    # 创建Cookie管理器
    cookie_manager = GoogleCookie()
    
    # 检查Cookie文件是否存在
    print(f"Cookie文件: {cookie_manager.cookie_file}")
    print(f"Cookie文件存在: {os.path.exists(cookie_manager.cookie_file)}")
    
    # 显示当前cookies
    current_cookies = cookie_manager.get_cookies()
    print(f"当前cookies数量: {len(current_cookies)}")
    print(f"Cookie键: {list(current_cookies.keys())}")
    
    # 检查cookies基本有效性
    is_valid = cookie_manager.is_cookies_valid()
    print(f"Cookies基本验证: {'有效' if is_valid else '无效'}")
    
    if not is_valid:
        print("Cookies无效，需要重新获取")
        return False
    
    return True

def test_google_search_with_cookies():
    """使用当前cookies测试Google搜索"""
    print("\n=== 测试Google搜索 ===")
    
    cookie_manager = GoogleCookie()
    cookies = cookie_manager.get_cookies()
    
    if not cookies:
        print("没有可用的cookies")
        return False
    
    # 构建请求
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    params = {
        'q': 'python programming',
        'hl': 'zh-CN',
        'num': 10
    }
    
    try:
        print("发送搜索请求...")
        response = requests.get(
            'https://www.google.com/search',
            params=params,
            headers=headers,
            cookies=cookies,
            timeout=15
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应URL: {response.url}")
        print(f"响应长度: {len(response.text)}")
        
        # 保存响应到文件用于调试
        with open('google_response_debug.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("响应已保存到 google_response_debug.html")
        
        if response.status_code == 200:
            # 检查是否被重定向到验证页面
            if 'sorry' in response.url.lower():
                print("❌ 被重定向到验证页面")
                return False
            
            # 检查响应内容
            blocked_keywords = ['captcha', 'unusual traffic', 'verify you are human', 'robot']
            response_text = response.text.lower()
            
            for keyword in blocked_keywords:
                if keyword in response_text:
                    print(f"❌ 检测到阻止关键词: {keyword}")
                    return False
            
            # 解析HTML
            page = etree.HTML(response.text)
            
            # 检查搜索结果
            search_results = page.xpath('//div[@class="g"] | //div[contains(@class, "MjjYud")] | //h3')
            print(f"找到搜索结果元素: {len(search_results)} 个")
            
            # 检查分页元素
            pagination_links = page.xpath('//a[contains(@href, "start=")]')
            print(f"找到分页链接: {len(pagination_links)} 个")
            
            # 检查页面标题
            title_elements = page.xpath('//title/text()')
            if title_elements:
                title = title_elements[0]
                print(f"页面标题: {title}")
            
            if len(search_results) > 0:
                print("✅ Cookie有效，能够获取搜索结果")
                return True
            else:
                print("❌ 没有找到搜索结果，Cookie可能无效")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return False

def refresh_cookies():
    """刷新cookies"""
    print("\n=== 刷新Cookies ===")
    
    cookie_manager = GoogleCookie()
    
    try:
        print("正在获取新的cookies...")
        new_cookies = cookie_manager.create_cookies()
        
        if new_cookies:
            print(f"✅ 成功获取新cookies，数量: {len(new_cookies)}")
            print(f"新Cookie键: {list(new_cookies.keys())}")
            return True
        else:
            print("❌ 获取新cookies失败")
            return False
            
    except Exception as e:
        print(f"❌ 获取cookies出错: {e}")
        return False

def main():
    """主函数"""
    print("Google Cookie 测试工具")
    print("=" * 50)
    
    # 1. 测试当前cookie有效性
    cookie_valid = test_cookie_validity()
    
    # 2. 测试Google搜索
    search_success = test_google_search_with_cookies()
    
    # 3. 如果测试失败，尝试刷新cookies
    if not cookie_valid or not search_success:
        print("\n当前cookies无效，尝试刷新...")
        refresh_success = refresh_cookies()
        
        if refresh_success:
            print("\n重新测试搜索...")
            search_success = test_google_search_with_cookies()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"Cookie基本验证: {'✅ 通过' if cookie_valid else '❌ 失败'}")
    print(f"搜索功能测试: {'✅ 通过' if search_success else '❌ 失败'}")
    
    if search_success:
        print("\n🎉 Cookie工作正常，可以进行搜索！")
    else:
        print("\n⚠️  Cookie存在问题，需要进一步调试")
        print("建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用代理")
        print("3. 检查是否被IP限制")
        print("4. 手动访问Google确认是否需要验证")

if __name__ == "__main__":
    main()
