#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTML获取测试
"""

import sys
import os
import requests
from lxml import etree

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_request():
    """直接测试请求Google搜索"""
    print("=== 直接测试Google搜索请求 ===")
    
    # 模拟真实浏览器的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://www.google.com/'
    }
    
    # 搜索参数
    params = {
        'q': 'python programming',
        'hl': 'zh-CN',
        'gl': 'CN',
        'lr': 'lang_zh-CN|lang_en',
        'num': 10,
        'start': 0,
        'ie': 'UTF-8',
        'oe': 'UTF-8',
        'source': 'hp'
    }
    
    try:
        print("🔍 发送搜索请求...")
        response = requests.get(
            'https://www.google.com/search',
            headers=headers,
            params=params,
            timeout=15
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应URL: {response.url}")
        print(f"📊 响应大小: {len(response.text)} 字符")
        
        # 保存HTML
        with open('direct_test_google.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("💾 HTML已保存到 direct_test_google.html")
        
        # 分析HTML
        analyze_html(response.text)
        
        return response.text
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def analyze_html(html_content):
    """分析HTML内容"""
    print("\n📋 HTML内容分析:")
    
    # 基本检查
    checks = [
        ("包含Google标识", "google" in html_content.lower()),
        ("包含搜索关键词", "python" in html_content.lower()),
        ("不是错误页面", "sorry" not in html_content.lower()),
        ("不是验证码页面", "captcha" not in html_content.lower()),
        ("HTML结构完整", "<html" in html_content and "</html>" in html_content),
        ("包含搜索结果", any(cls in html_content for cls in ['class="g"', 'class="MjjYud"', "<h3"])),
        ("包含分页元素NKTSme", 'class="NKTSme"' in html_content),
        ("包含分页链接", "start=" in html_content),
        ("包含导航元素", any(nav in html_content for nav in ["<nav", "role=\"navigation\""])),
        ("内容充足", len(html_content) > 50000)
    ]
    
    for name, result in checks:
        status = "✅" if result else "❌"
        print(f"  {status} {name}")
    
    # 统计
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    score = (passed / total) * 100
    
    print(f"\n📈 HTML质量评分: {score:.1f}% ({passed}/{total})")
    
    # 详细统计
    print(f"\n🔍 详细统计:")
    print(f"  - HTML大小: {len(html_content):,} 字符")
    print(f"  - <div>标签: {html_content.count('<div')} 个")
    print(f"  - <a>链接: {html_content.count('<a')} 个")
    print(f"  - class属性: {html_content.count('class=')} 个")
    # print(f"  - NKTSme类: {html_content.count('class=\"NKTSme\"')} 个")
    print(f"  - start=参数: {html_content.count('start=')} 个")
    
    # 尝试提取页数
    try_extract_pages(html_content)

def try_extract_pages(html_content):
    """尝试提取页数"""
    print(f"\n🎯 尝试提取页数:")
    
    try:
        page = etree.HTML(html_content)
        
        # 方法1: 原有的XPath
        try:
            pages_elem = page.xpath('//td[@class="NKTSme"][last()]/a/text()')
            if pages_elem:
                pages = int(''.join(pages_elem))
                print(f"  ✅ 方法1成功: {pages} 页")
                return pages
            else:
                print(f"  ❌ 方法1失败: 未找到NKTSme元素")
        except Exception as e:
            print(f"  ❌ 方法1出错: {e}")
        
        # 方法2: 查找所有包含数字的链接
        try:
            import re
            links = page.xpath('//a[contains(@href, "start=")]')
            max_page = 1
            for link in links:
                href = link.get('href', '')
                text = link.text_content().strip()
                
                if text.isdigit():
                    max_page = max(max_page, int(text))
                
                start_match = re.search(r'start=(\d+)', href)
                if start_match:
                    start_num = int(start_match.group(1))
                    page_num = (start_num // 10) + 1
                    max_page = max(max_page, page_num)
            
            if max_page > 1:
                pages = min(max_page, 10)
                print(f"  ✅ 方法2成功: {pages} 页")
                return pages
            else:
                print(f"  ❌ 方法2失败: 未找到分页链接")
        except Exception as e:
            print(f"  ❌ 方法2出错: {e}")
        
        # 方法3: 检查是否有搜索结果
        try:
            results = page.xpath('//div[@class="g"] | //div[contains(@class, "MjjYud")] | //h3')
            if results:
                print(f"  ✅ 方法3: 找到 {len(results)} 个搜索结果，至少1页")
                return 1
            else:
                print(f"  ❌ 方法3失败: 未找到搜索结果")
        except Exception as e:
            print(f"  ❌ 方法3出错: {e}")
        
        print(f"  ❌ 所有方法都失败")
        return 0
        
    except Exception as e:
        print(f"  ❌ HTML解析失败: {e}")
        return 0

def main():
    """主函数"""
    print("Google搜索HTML获取质量测试")
    print("=" * 60)
    print("此测试将直接发送请求到Google，分析返回的HTML质量")
    print("=" * 60)
    
    html_content = test_direct_request()
    
    print("\n" + "=" * 60)
    if html_content:
        print("🎉 测试完成！请查看分析结果")
        print("💡 如果分页元素检测失败，说明需要进一步优化请求策略")
    else:
        print("🚨 测试失败！无法获取HTML内容")

if __name__ == "__main__":
    main()
