#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Google搜索的脚本
"""

import requests
from lxml import etree

def simple_test():
    """简单测试Google搜索"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    }
    
    params = {
        'q': 'python programming',
        'num': 10
    }
    
    try:
        print("开始请求Google搜索...")
        response = requests.get(
            'https://www.google.com/search',
            params=params,
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        if response.status_code == 200:
            # 保存HTML到文件
            with open('google_response.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("HTML已保存到 google_response.html")
            
            # 解析HTML
            page = etree.HTML(response.text)
            
            # 测试原有的XPath
            old_xpath = '//td[@class="NKTSme"][last()]/a/text()'
            old_result = page.xpath(old_xpath)
            print(f"原XPath结果: {old_result}")
            
            # 查找所有td元素
            all_tds = page.xpath('//td')
            print(f"找到 {len(all_tds)} 个td元素")
            
            # 查找包含数字的链接
            number_links = page.xpath('//a[contains(text(), "1") or contains(text(), "2") or contains(text(), "3") or contains(text(), "4") or contains(text(), "5")]')
            print(f"找到 {len(number_links)} 个包含数字的链接")
            for link in number_links[:5]:
                text = link.text_content().strip()
                print(f"  - '{text}'")
                
        else:
            print(f"请求失败: {response.status_code}")
            print(f"响应内容前500字符: {response.text[:500]}")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
