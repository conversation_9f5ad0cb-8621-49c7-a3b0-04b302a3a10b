# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/15 上午10:41
File Name:xpath_test.py
"""
from lxml import etree

# 打开D:\crawler\email_page\page.html文件
with open("D:\crawler\email_page\page.html", "r", encoding="utf-8") as f:
    response = f.read()

page = etree.HTML(response)
pages = ''.join(page.xpath('//td[@class="NKTSme"]'))
if pages:
    pages = int(pages)
else:
    pages = 1
print('总页数', pages)