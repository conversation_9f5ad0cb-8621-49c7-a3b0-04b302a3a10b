#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Google搜索页面结构的脚本
用于检查当前Google搜索结果页面的HTML结构，特别是分页部分
"""

import requests
import time
from lxml import etree
import json

def test_google_search():
    """测试Google搜索并分析页面结构"""
    
    # 模拟浏览器的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    # 测试搜索关键词
    test_keywords = [
        'python programming',
        'machine learning',
        'web development'
    ]
    
    for keyword in test_keywords:
        print(f"\n=== 测试关键词: {keyword} ===")
        
        # 构建搜索URL
        params = {
            'q': keyword,
            'hl': 'zh-CN',
            'lr': 'lang_zh-CN|lang_en',
            'num': 10
        }
        
        try:
            # 发送请求
            response = requests.get(
                'https://www.google.com/search',
                params=params,
                headers=headers,
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 解析HTML
                page = etree.HTML(response.text)
                
                # 保存HTML到文件用于调试
                with open(f'google_search_{keyword.replace(" ", "_")}.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # 测试原有的XPath选择器
                print("=== 测试原有XPath选择器 ===")
                old_xpath = '//td[@class="NKTSme"][last()]/a/text()'
                old_result = page.xpath(old_xpath)
                print(f"原XPath结果: {old_result}")
                
                # 尝试其他可能的分页选择器
                print("\n=== 尝试其他分页选择器 ===")
                
                # 常见的分页选择器
                pagination_xpaths = [
                    '//a[@aria-label="Page"]//text()',
                    '//div[@role="navigation"]//a//text()',
                    '//table[@role="presentation"]//td//a//text()',
                    '//span[contains(@class, "SJajHc")]//text()',
                    '//a[contains(@aria-label, "Page")]/@aria-label',
                    '//div[contains(@class, "AaVjTc")]//a//text()',
                    '//nav//a//text()',
                    '//td[contains(@class, "d6cvqb")]//a//text()',
                    '//a[contains(@href, "start=")]//text()',
                ]
                
                for i, xpath in enumerate(pagination_xpaths):
                    try:
                        result = page.xpath(xpath)
                        if result:
                            print(f"XPath {i+1}: {xpath}")
                            print(f"结果: {result[:10]}")  # 只显示前10个结果
                            print()
                    except Exception as e:
                        print(f"XPath {i+1} 错误: {e}")
                
                # 查找所有包含数字的链接
                print("=== 查找所有包含数字的链接 ===")
                number_links = page.xpath('//a[contains(@href, "start=") or contains(text(), "1") or contains(text(), "2") or contains(text(), "3")]')
                for link in number_links[:5]:  # 只显示前5个
                    text = link.text_content().strip() if link.text_content() else ""
                    href = link.get('href', '')
                    if text and (text.isdigit() or 'Page' in text):
                        print(f"链接文本: '{text}', href: {href[:100]}...")
                
                # 查找分页相关的div和table
                print("\n=== 查找分页相关的结构 ===")
                nav_elements = page.xpath('//nav | //table[@role="presentation"] | //div[contains(@class, "nav")] | //div[@role="navigation"]')
                for i, elem in enumerate(nav_elements[:3]):
                    print(f"导航元素 {i+1}: {elem.tag}, class: {elem.get('class', '')}, role: {elem.get('role', '')}")
                    # 查找其中的链接
                    links = elem.xpath('.//a')
                    for link in links[:5]:
                        text = link.text_content().strip()
                        if text and (text.isdigit() or 'Next' in text or 'Previous' in text):
                            print(f"  - 链接: '{text}'")
                
            else:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:500]}...")
                
        except Exception as e:
            print(f"请求出错: {e}")
        
        # 避免请求过于频繁
        time.sleep(2)

if __name__ == "__main__":
    test_google_search()
