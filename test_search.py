#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的Google搜索功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_page.spider import GoogleSearch
import pandas as pd

class MockUpdateLog:
    """模拟日志更新函数"""
    def emit(self, message):
        print(f"LOG: {message}")

def test_single_search():
    """测试单个搜索"""
    print("=== 测试单个搜索 ===")
    
    # 创建搜索实例
    searcher = GoogleSearch(
        thread_id=0,
        update_log=MockUpdateLog()
    )
    
    # 测试关键词
    test_keywords = [
        '"python programming"',
        '"machine learning"',
        '"web development"'
    ]
    
    for keyword in test_keywords:
        print(f"\n测试关键词: {keyword}")
        try:
            result = searcher.search(keyword)
            print(f"结果: {result} 页")
        except Exception as e:
            print(f"错误: {e}")
            import traceback
            traceback.print_exc()

def test_excel_processing():
    """测试Excel文件处理"""
    print("\n=== 测试Excel文件处理 ===")
    
    # 创建测试Excel文件
    test_data = {
        'query': [
            'python programming',
            'machine learning',
            'web development',
            'data science',
            'artificial intelligence'
        ],
        'pages': [None, None, None, None, None]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_search.xlsx'
    df.to_excel(test_file, index=False)
    print(f"创建测试文件: {test_file}")
    
    # 测试处理前几个关键词
    from email_page.spider import GoogleSearchThreadManager
    
    try:
        manager = GoogleSearchThreadManager(
            num_threads=1,
            excel_file=test_file,
            update_log=MockUpdateLog()
        )
        
        # 只处理前2个查询进行测试
        print("开始测试搜索...")
        started = manager.start()
        
        if started:
            manager.wait_completion()
            print("测试完成!")
            
            # 查看结果
            result_df = pd.read_excel(test_file)
            print("\n结果:")
            print(result_df)
        else:
            print("没有需要处理的查询")
            
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("Google搜索功能测试")
    print("=" * 50)
    
    # 测试单个搜索
    test_single_search()
    
    # 测试Excel处理
    test_excel_processing()

if __name__ == "__main__":
    main()
