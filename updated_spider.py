# -*- coding: utf-8 -*-
"""
更新的Google搜索爬虫
解决抓取不到数据的问题
"""
import random
import requests
import threading
import pandas as pd
import os
import time
import re
from lxml import etree
from email_page.cookies import GoogleCookie
from proxy import ProxyManager


class UpdatedGoogleSearch:
    def __init__(self, thread_id=0, cookie_file=None, proxy_manager=None, update_log=None):
        """
        更新的GoogleSearch类，解决抓取问题
        """
        folder = 'cookies'
        if not os.path.exists(folder):
            os.makedirs(folder)
        if cookie_file is None:
            cookie_file = f"cookies/google_cookies_{thread_id}.json"
        
        self.update_log = update_log
        self.thread_id = thread_id
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)
        self.proxy_manager = proxy_manager
        
        # 更新的User-Agent列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        # 更新的请求头
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        self.lock = threading.Lock()

    def get_headers(self):
        """生成随机请求头"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(self.user_agents)
        return headers

    def extract_page_count(self, html_content):
        """
        从HTML内容中提取页数，使用多种方法
        """
        try:
            page = etree.HTML(html_content)
            
            # 方法1: 原有的XPath选择器
            try:
                pages_elem = page.xpath('//td[@class="NKTSme"][last()]/a/text()')
                if pages_elem:
                    return int(''.join(pages_elem))
            except:
                pass
            
            # 方法2: 查找分页导航中的数字链接
            try:
                # 查找所有包含start参数的链接
                pagination_links = page.xpath('//a[contains(@href, "start=")]')
                max_page = 1
                for link in pagination_links:
                    href = link.get('href', '')
                    text = link.text_content().strip()
                    
                    # 从链接文本中提取数字
                    if text.isdigit():
                        max_page = max(max_page, int(text))
                    
                    # 从href中提取start参数计算页数
                    start_match = re.search(r'start=(\d+)', href)
                    if start_match:
                        start_num = int(start_match.group(1))
                        page_num = (start_num // 10) + 1
                        max_page = max(max_page, page_num)
                
                if max_page > 1:
                    return min(max_page, 10)  # 限制最大10页
            except:
                pass
            
            # 方法3: 查找aria-label包含Page的链接
            try:
                page_links = page.xpath('//a[contains(@aria-label, "Page")]')
                max_page = 1
                for link in page_links:
                    aria_label = link.get('aria-label', '')
                    # 提取"Page X"中的数字
                    page_match = re.search(r'Page (\d+)', aria_label)
                    if page_match:
                        page_num = int(page_match.group(1))
                        max_page = max(max_page, page_num)
                
                if max_page > 1:
                    return min(max_page, 10)
            except:
                pass
            
            # 方法4: 查找导航区域的数字
            try:
                nav_numbers = page.xpath('//nav//a//text() | //div[@role="navigation"]//a//text()')
                max_page = 1
                for text in nav_numbers:
                    text = text.strip()
                    if text.isdigit() and 1 <= int(text) <= 10:
                        max_page = max(max_page, int(text))
                
                if max_page > 1:
                    return max_page
            except:
                pass
            
            # 方法5: 正则表达式搜索页面中的分页信息
            try:
                # 搜索类似 "Page 1 of 10" 或 "1 / 10" 的模式
                page_pattern = re.search(r'(?:Page\s+\d+\s+of\s+(\d+)|(\d+)\s*/\s*(\d+))', html_content)
                if page_pattern:
                    if page_pattern.group(1):
                        return min(int(page_pattern.group(1)), 10)
                    elif page_pattern.group(3):
                        return min(int(page_pattern.group(3)), 10)
            except:
                pass
            
            # 如果所有方法都失败，检查是否有搜索结果
            results = page.xpath('//div[@class="g"] | //div[contains(@class, "MjjYud")]')
            if results:
                return 1  # 至少有1页结果
            else:
                return 0  # 没有结果
                
        except Exception as e:
            print(f"[线程 {self.thread_id}] 解析页面出错: {e}")
            return 1  # 默认返回1页

    def send_request(self, url, params, cookies, timeout=15, max_retries=3):
        """
        发送HTTP请求，增强版本
        """
        headers = self.get_headers()
        
        for attempt in range(max_retries):
            try:
                # 添加随机延迟
                if attempt > 0:
                    delay = random.uniform(2, 5)
                    time.sleep(delay)
                
                # 代理设置（如果需要）
                proxy_server = None
                if self.proxy_manager:
                    proxy_ip_port = self.proxy_manager.get_random_proxy()
                    if proxy_ip_port:
                        proxy_server = {
                            "http": f"http://{proxy_ip_port}",
                            "https": f"http://{proxy_ip_port}"
                        }
                
                # 发送请求
                response = requests.get(
                    url,
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=timeout,
                    proxies=proxy_server,
                    allow_redirects=True
                )
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:
                    print(f"[线程 {self.thread_id}] 请求过于频繁，等待重试...")
                    if self.update_log:
                        self.update_log.emit(f"[线程 {self.thread_id}] 请求过于频繁，等待重试...")
                    time.sleep(random.uniform(10, 20))
                else:
                    print(f"[线程 {self.thread_id}] HTTP状态码: {response.status_code}")
                    if self.update_log:
                        self.update_log.emit(f"[线程 {self.thread_id}] HTTP状态码: {response.status_code}")
                
            except requests.exceptions.Timeout:
                print(f"[线程 {self.thread_id}] 请求超时，重试 {attempt + 1}/{max_retries}")
                if self.update_log:
                    self.update_log.emit(f"[线程 {self.thread_id}] 请求超时，重试 {attempt + 1}/{max_retries}")
            except Exception as e:
                print(f"[线程 {self.thread_id}] 请求错误: {e}, 重试 {attempt + 1}/{max_retries}")
                if self.update_log:
                    self.update_log.emit(f"[线程 {self.thread_id}] 请求错误: {e}, 重试 {attempt + 1}/{max_retries}")
        
        return None

    def search(self, keyword, max_cookie_retries=3):
        """
        搜索关键词并返回页数
        """
        # 构建搜索参数
        params = {
            'q': keyword,
            'hl': 'zh-CN',
            'lr': 'lang_zh-CN|lang_en',
            'num': 10,
            'start': 0
        }
        
        for attempt in range(max_cookie_retries):
            try:
                cookies = self.google_cookie.cookies
                
                response = self.send_request(
                    url='https://www.google.com/search',
                    params=params,
                    cookies=cookies
                )
                
                if not response:
                    print(f"[线程 {self.thread_id}] 第 {attempt + 1}/{max_cookie_retries} 次尝试失败，重新获取cookies")
                    if self.update_log:
                        self.update_log.emit(f"[线程 {self.thread_id}] 第 {attempt + 1}/{max_cookie_retries} 次尝试失败，重新获取cookies")
                    
                    with self.lock:
                        self.google_cookie.cookies = self.google_cookie.create_cookies()
                    continue
                
                # 检查是否被重定向或阻止
                if 'sorry' in response.url.lower() or 'captcha' in response.text.lower():
                    print(f"[线程 {self.thread_id}] 检测到验证码或IP被限制")
                    if self.update_log:
                        self.update_log.emit(f"[线程 {self.thread_id}] 检测到验证码或IP被限制")
                    time.sleep(random.uniform(30, 60))
                    continue
                
                # 提取页数
                pages = self.extract_page_count(response.text)
                
                print(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}')
                if self.update_log:
                    self.update_log.emit(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}')
                
                return pages
                
            except Exception as e:
                print(f"[线程 {self.thread_id}] 搜索出错: {e}")
                if self.update_log:
                    self.update_log.emit(f"[线程 {self.thread_id}] 搜索出错: {e}")
        
        print(f"[线程 {self.thread_id}] 所有 {max_cookie_retries} 次尝试均失败")
        if self.update_log:
            self.update_log.emit(f"[线程 {self.thread_id}] 所有 {max_cookie_retries} 次尝试均失败")
        return None
