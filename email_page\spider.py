# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/9 13:15
File Name: spider.py
"""
import random
import requests
import threading
import pandas as pd
import os
from lxml import etree
from email_page.cookies import GoogleCookie
try:
    from email_page.proxy import ProxyManager
except ImportError:
    try:
        from proxy import ProxyManager
    except ImportError:
        # 如果没有proxy模块，创建一个简单的替代
        class ProxyManager:
            def __init__(self, num=500):
                pass
            def get_random_proxy(self):
                return None


class GoogleSearch:
    def __init__(self, thread_id=0, cookie_file=None, proxy_manager=None):
        """
        Initialize the GoogleSearch class

        Args:
            thread_id: 线程ID,用于区分不同实例
            cookie_file: Cookie文件路径 (默认: 基于thread_id生成不同的文件名)
            proxy_manager: 代理管理器实例 (默认: 创建新的MyProxyManager)
        """
        # 如果没有提供cookie_file，则基于thread_id生成唯一文件名
        folder = 'cookies'
        if not os.path.exists(folder):
            os.makedirs(folder)
        if cookie_file is None:
            cookie_file = f"cookies/google_cookies_{thread_id}.json"

        self.thread_id = thread_id
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)
        self.proxy_manager = proxy_manager
        self.failure_count = 0  # 失败计数器
        self.max_failures = 10  # 最大失败次数

        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]

        # 更真实的请求头，减少自动化特征
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }

        # 创建Session保持连接
        self.session = requests.Session()
        self.session.headers.update(self.base_headers)
        self.lock = threading.Lock()  # 添加线程锁用于同步操作

    def get_headers(self, add_referer=True):
        """生成带有随机User-Agent的请求头"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(self.user_agents)

        # 添加Referer，模拟从Google主页搜索
        if add_referer:
            headers['Referer'] = 'https://www.google.com/'

        return headers

    def send_request(self, url, params, cookies, timeout=10, max_retries=2):
        """
        发送HTTP请求，带有重试机制

        Args:
            url: 目标URL
            params: URL参数
            cookies: 请求使用的Cookies
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数

        Returns:
            Response对象，失败则返回None
        """
        headers = self.get_headers()

        for attempt in range(max_retries):
            try:
                # proxy_ip_port = self.proxy_manager.get_random_proxy()
                proxy_ip_port = None
                if proxy_ip_port:
                    # proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
                    #     "user": 'A1LGFVXJ',
                    #     "password": 'DDC83DD434F5',
                    #     "server": proxy_ip_port,
                    # }
                    # proxy_server = {"http": proxyUrl}
                    proxy_server = {
                        "http": f"http://{proxy_ip_port}",
                        "https": f"http://{proxy_ip_port}"
                    }
                    print(f"[线程 {self.thread_id}] 使用代理: {proxy_ip_port}")
                else:
                    print(f"[线程 {self.thread_id}] 没有可用代理")
                    proxy_server = None
                # 更新Session的headers和cookies
                self.session.headers.update(headers)
                self.session.cookies.update(cookies)

                res = self.session.get(
                    url,
                    params=params,
                    timeout=timeout,
                    proxies=proxy_server,
                    allow_redirects=True
                )

                if res.status_code == 200:
                    return res
                elif res.status_code == 429:
                    print(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}")
                else:
                    print(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}")

            except Exception as e:
                print(f'[线程 {self.thread_id}] {url} 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次')

            # 重试前等待递增时间，避免被检测
            if attempt < max_retries - 1:  # 最后一次不需要等待
                import time
                wait_time = random.uniform(2, 5) * (attempt + 1)  # 递增等待时间
                print(f"[线程 {self.thread_id}] 等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

        print(f"[线程 {self.thread_id}] 请求 {url} 失败，已重试 {max_retries} 次")
        return None

    def check_cookies_valid(self, page):
        h3_elements = page.xpath('//h3')
        return len(h3_elements) >= 1

    def extract_page_count(self, html_content):
        """
        详细的页数提取方法，使用多种策略
        """
        try:
            page = etree.HTML(html_content)

            # 方法1: 原有的XPath选择器
            try:
                pages_elem = page.xpath('//td[@class="NKTSme"][last()]/a/text()')
                if pages_elem:
                    pages = int(''.join(pages_elem))
                    print(f"[线程 {self.thread_id}] 方法1成功提取页数: {pages}")
                    return pages
            except Exception as e:
                print(f"[线程 {self.thread_id}] 方法1失败: {e}")

            # 方法2: 查找分页导航中的数字链接
            try:
                import re
                pagination_links = page.xpath('//a[contains(@href, "start=")]')
                max_page = 1
                for link in pagination_links:
                    href = link.get('href', '')
                    text = link.text_content().strip()

                    # 从链接文本中提取数字
                    if text.isdigit():
                        max_page = max(max_page, int(text))

                    # 从href中提取start参数计算页数
                    start_match = re.search(r'start=(\d+)', href)
                    if start_match:
                        start_num = int(start_match.group(1))
                        page_num = (start_num // 10) + 1
                        max_page = max(max_page, page_num)

                if max_page > 1:
                    pages = min(max_page, 10)  # 限制最大10页
                    print(f"[线程 {self.thread_id}] 方法2成功提取页数: {pages}")
                    return pages
            except Exception as e:
                print(f"[线程 {self.thread_id}] 方法2失败: {e}")

            # 方法3: 查找aria-label包含Page的链接
            try:
                import re
                page_links = page.xpath('//a[contains(@aria-label, "Page")]')
                max_page = 1
                for link in page_links:
                    aria_label = link.get('aria-label', '')
                    page_match = re.search(r'Page (\d+)', aria_label)
                    if page_match:
                        page_num = int(page_match.group(1))
                        max_page = max(max_page, page_num)

                if max_page > 1:
                    pages = min(max_page, 10)
                    print(f"[线程 {self.thread_id}] 方法3成功提取页数: {pages}")
                    return pages
            except Exception as e:
                print(f"[线程 {self.thread_id}] 方法3失败: {e}")

            # 方法4: 查找导航区域的数字
            try:
                nav_numbers = page.xpath('//nav//a//text() | //div[@role="navigation"]//a//text()')
                max_page = 1
                for text in nav_numbers:
                    text = text.strip()
                    if text.isdigit() and 1 <= int(text) <= 10:
                        max_page = max(max_page, int(text))

                if max_page > 1:
                    print(f"[线程 {self.thread_id}] 方法4成功提取页数: {max_page}")
                    return max_page
            except Exception as e:
                print(f"[线程 {self.thread_id}] 方法4失败: {e}")

            # 方法5: 检查是否有搜索结果
            try:
                results = page.xpath('//div[@class="g"] | //div[contains(@class, "MjjYud")] | //h3')
                if results:
                    print(f"[线程 {self.thread_id}] 方法5: 找到搜索结果，返回1页")
                    return 1  # 至少有1页结果
                else:
                    print(f"[线程 {self.thread_id}] 方法5: 没有找到搜索结果")
                    return 0  # 没有结果
            except Exception as e:
                print(f"[线程 {self.thread_id}] 方法5失败: {e}")

        except Exception as e:
            print(f"[线程 {self.thread_id}] 解析页面出错: {e}")

        print(f"[线程 {self.thread_id}] 所有方法都失败，返回默认值1")
        return 1  # 默认返回1页

    def search(self, keyword):
        """
        搜索关键词并返回页数
        实现累计失败策略：10次失败才更新cookie
        """
        # 检查cookie不能为空
        cookies = self.google_cookie.cookies
        if not cookies:
            print(f"[线程 {self.thread_id}] Cookie为空，需要获取新cookie")
            with self.lock:
                self.google_cookie.cookies = self.google_cookie.create_cookies(keyword)
            cookies = self.google_cookie.cookies

        # 更完整的搜索参数，模拟真实浏览器搜索
        params = {
            'q': keyword,
            'hl': 'zh-CN',
            'gl': 'CN',  # 地理位置
            'lr': 'lang_zh-CN|lang_en',
            'num': 10,
            'start': 0,
            'ie': 'UTF-8',
            'oe': 'UTF-8',
            'source': 'hp',  # 来源：主页
            'ei': '',  # 可以为空
            'iflsig': '',  # 可以为空
        }

        response = self.send_request(
            url='https://www.google.com/search',
            params=params,
            cookies=cookies
        )

        if not response:
            # 失败计数增加
            self.failure_count += 1
            print(f"[线程 {self.thread_id}] 搜索失败，失败计数: {self.failure_count}/{self.max_failures}")

            # 累计10次失败才更新cookie
            if self.failure_count >= self.max_failures:
                print(f"[线程 {self.thread_id}] 达到最大失败次数，更新cookie")
                with self.lock:
                    # 使用当前关键词更新cookie
                    self.google_cookie.cookies = self.google_cookie.create_cookies(keyword)
                self.failure_count = 0  # 重置失败计数

            return None

        # 成功获取响应，重置失败计数
        if self.failure_count > 0:
            print(f"[线程 {self.thread_id}] 搜索成功，重置失败计数")
            self.failure_count = 0

        # 调试：保存HTML到文件查看
        self.save_debug_html(response.text, keyword)

        # 使用详细的页数提取方法
        pages = self.extract_page_count(response.text)

        print(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}')
        return pages

    def save_debug_html(self, html_content, keyword):
        """保存HTML到文件用于调试"""
        try:
            import os
            debug_dir = "debug_html"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            # 清理关键词作为文件名
            safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_keyword = safe_keyword.replace(' ', '_')[:50]  # 限制长度

            filename = f"{debug_dir}/google_search_{self.thread_id}_{safe_keyword}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"[线程 {self.thread_id}] HTML已保存到: {filename}")

            # 简单分析HTML内容
            if 'sorry' in html_content.lower():
                print(f"[线程 {self.thread_id}] ⚠️ 检测到'sorry'页面，可能被限制")
            if 'captcha' in html_content.lower():
                print(f"[线程 {self.thread_id}] ⚠️ 检测到验证码页面")
            if len(html_content) < 10000:
                print(f"[线程 {self.thread_id}] ⚠️ HTML内容过短({len(html_content)}字符)，可能不完整")
            if 'class="NKTSme"' in html_content:
                print(f"[线程 {self.thread_id}] ✅ 找到分页元素NKTSme")
            else:
                print(f"[线程 {self.thread_id}] ❌ 未找到分页元素NKTSme")

        except Exception as e:
            print(f"[线程 {self.thread_id}] 保存调试HTML失败: {e}")


class GoogleSearchThreadManager:
    def __init__(self, num_threads=5, excel_file=None, browser_path=None):
        """
        初始化多线程搜索管理器

        Args:
            num_threads: 线程数
            excel_file: Excel文件路径，包含查询语句，同时也是结果文件
            browser_path: 浏览器路径
        """
        self.num_threads = num_threads
        self.excel_file = excel_file
        self.result_lock = threading.Lock()
        self.threads = []
        self.thread_queries = []  # 每个线程负责的查询列表
        self.browser_path = browser_path

        # 进度跟踪
        self.total_queries = 0
        self.completed_queries = 0
        self.progress_callback = None

        # 初始化结果文件
        self.initialize_excel_file()

    def initialize_excel_file(self):
        """初始化Excel文件，确保有pages列"""
        if not self.excel_file or not os.path.exists(self.excel_file):
            raise FileNotFoundError(f"Excel文件不存在: {self.excel_file}")

        try:
            # 读取Excel文件
            self.results_df = pd.read_excel(self.excel_file)

            # 检查是否有pages列，如果没有则添加
            if 'pages' not in self.results_df.columns:
                self.results_df['pages'] = None
                # 保存回文件
                self.results_df.to_excel(self.excel_file, index=False)
                print(f"在Excel文件中添加了pages列")

            print(f"成功加载Excel文件: {self.excel_file}")

        except Exception as e:
            print(f"初始化Excel文件失败: {e}")
            raise

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def update_progress(self, thread_id, completed):
        """更新并报告当前进度"""
        with self.result_lock:
            self.completed_queries += completed
            if self.progress_callback and self.total_queries > 0:
                progress = (self.completed_queries / self.total_queries) * 100
                self.progress_callback(self.completed_queries, self.total_queries, progress)
            print(f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")

    def distribute_queries(self):
        """将查询分配给各个线程"""
        try:
            # 重新读取Excel文件，确保获取最新状态
            self.results_df = pd.read_excel(self.excel_file)
            print("总共有", len(self.results_df), "条数据")
            # 获取所有未处理的查询
            unprocessed_queries = []
            for index, row in self.results_df.iterrows():
                query = row['query']
                pages = row['pages']
                if pd.isna(pages):  # 只添加未处理的查询
                    unprocessed_queries.append((index, query))

            self.total_queries = len(unprocessed_queries)
            print(f"共有 {self.total_queries} 个查询需要处理")

            if self.total_queries == 0:
                return []

            # 将查询平均分配给各个线程
            self.thread_queries = [[] for _ in range(self.num_threads)]
            for i, (index, query) in enumerate(unprocessed_queries):
                thread_idx = i % self.num_threads
                self.thread_queries[thread_idx].append((index, query))

            # 打印每个线程分配的查询数量
            for i, queries in enumerate(self.thread_queries):
                print(f"线程 {i} 分配了 {len(queries)} 个查询")

            return self.thread_queries
        except Exception as e:
            print(f"分配查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def worker(self, thread_id, queries):
        """
        工作线程函数

        Args:
            thread_id: 线程ID
            queries: 该线程负责处理的查询列表，每个元素是(index, query)元组
        """
        if not queries:
            print(f"[线程 {thread_id}] 没有分配到查询任务")
            return

        # 为每个线程创建一个GoogleSearch实例，使用唯一的cookie文件
        try:
            proxy_manager = ProxyManager(num=500)
        except:
            proxy_manager = None
        searcher = GoogleSearch(thread_id=thread_id, proxy_manager=proxy_manager)

        completed = 0
        local_results = {}  # 本地存储结果，减少文件访问 {index: pages}

        for index, query in queries:
            try:
                search_query = f'"{query}"'
                print(f"[线程 {thread_id}] 开始处理查询: {query}")

                # 执行搜索
                pages = searcher.search(search_query)

                # 无论成功失败都存储结果，失败时pages为None
                if pages is not None:
                    local_results[index] = pages
                    print(f"[线程 {thread_id}] 查询 {query} 成功，页数: {pages}")
                else:
                    print(f"[线程 {thread_id}] 查询 {query} 失败，跳过此查询")
                    # 失败的查询不存储结果，保持Excel中的空值，下次运行时会重新处理

                completed += 1

                # 每处理5个查询更新一次结果文件（只更新成功的）
                if len(local_results) >= 5 or completed == len(queries):
                    if local_results:  # 只有有结果时才更新
                        self.update_results_file(local_results, thread_id)
                        local_results = {}  # 清空本地结果
                    self.update_progress(thread_id, min(5, completed))

                # 添加随机延迟，避免请求过于频繁
                import time
                time.sleep(random.uniform(1, 3))

            except Exception as e:
                print(f"[线程 {thread_id}] 处理查询 {query} 时出错: {e}")
                completed += 1  # 即使出错也要计数，确保循环能完成

        # 处理剩余的结果
        if local_results:
            self.update_results_file(local_results, thread_id)
            self.update_progress(thread_id, len(local_results))

        print(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")

    def update_results_file(self, results_dict, thread_id):
        """
        更新结果文件中的特定查询结果

        Args:
            results_dict: 查询结果字典 {index: pages}
            thread_id: 线程ID
        """
        if not results_dict:
            return

        try:
            # 使用线程锁保护文件读写
            with self.result_lock:
                # 重新读取最新的Excel文件
                latest_df = pd.read_excel(self.excel_file)

                # 更新结果
                for index, pages in results_dict.items():
                    # 直接使用索引更新
                    latest_df.loc[index, 'pages'] = pages

                # 保存更新后的文件
                latest_df.to_excel(self.excel_file, index=False)

                # 更新内存中的DataFrame
                self.results_df = latest_df

            print(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到文件")
        except Exception as e:
            print(f"[线程 {thread_id}] 更新结果文件失败: {e}")
            import traceback
            traceback.print_exc()

    def start(self):
        """启动所有工作线程"""
        # 分配查询任务给各个线程
        thread_queries = self.distribute_queries()

        if not any(thread_queries):
            print("没有需要处理的新查询")
            return False

        # 创建并启动工作线程
        for i in range(self.num_threads):
            if i < len(thread_queries) and thread_queries[i]:
                thread = threading.Thread(target=self.worker, args=(i, thread_queries[i]))
                thread.daemon = True  # 设置为守护线程
                self.threads.append(thread)
                thread.start()
                print(f"启动线程 {i}")

        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        print("所有线程已完成!")

    def get_results(self):
        """获取搜索结果DataFrame"""
        # 重新读取Excel文件，确保获取最新结果
        try:
            return pd.read_excel(self.excel_file)
        except Exception as e:
            print(f"读取结果文件失败: {e}")
            return self.results_df


if __name__ == '__main__':
    # 配置参数（可以在这里直接修改，不需要命令行）
    excel_file = "../search_results_email.xlsx"  # Excel文件路径
    threads = 1  # 线程数量

    print(f"===== 谷歌搜索多线程工具 =====")
    print(f"Excel文件: {excel_file}")
    print(f"线程数量: {threads}")
    print("=" * 30)

    try:
        # 创建搜索管理器
        manager = GoogleSearchThreadManager(
            num_threads=threads,
            excel_file=excel_file
        )

        # 启动搜索
        print("开始分配查询任务...")
        started = manager.start()

        # 如果有任务需要执行
        if started:
            print("正在执行查询，请稍候...")
            manager.wait_completion()
            print("所有查询已完成!")
        else:
            print("没有需要处理的查询")

        print(f"\n处理完成: 共处理了 {manager.completed_queries} 个查询")
        print(f"结果已保存到 {excel_file}")

    except KeyboardInterrupt:
        print("\n程序被用户中断，当前结果已保存")
        print(f"您可以稍后重新运行程序继续执行")

    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback

        traceback.print_exc()

    # 程序结束前等待用户按键，方便查看输出
    input("\n按回车键退出...")
