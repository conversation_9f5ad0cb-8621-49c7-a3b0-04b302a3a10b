# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/9 13:15
File Name: spider.py
"""
import random
import requests
import threading
import pandas as pd
import os
from lxml import etree
from email_page.cookies import GoogleCookie
from proxy import ProxyManager


class GoogleSearch:
    def __init__(self, thread_id=0, cookie_file=None, proxy_manager=None, update_log=None):
        """
        Initialize the GoogleSearch class

        Args:
            thread_id: 线程ID,用于区分不同实例
            cookie_file: Cookie文件路径 (默认: 基于thread_id生成不同的文件名)
            proxy_manager: 代理管理器实例 (默认: 创建新的MyProxyManager)
        """
        # 如果没有提供cookie_file，则基于thread_id生成唯一文件名
        folder = 'cookies'
        if not os.path.exists(folder):
            os.makedirs(folder)
        if cookie_file is None:
            cookie_file = f"cookies/google_cookies_{thread_id}.json"
        self.update_log = update_log
        self.thread_id = thread_id
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)
        self.proxy_manager = proxy_manager
        # 更新的User-Agent列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        # 简化的请求头，避免过于明显的自动化特征
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        self.lock = threading.Lock()  # 添加线程锁用于同步操作

    def get_headers(self):
        """生成带有随机User-Agent的请求头"""
        headers = self.base_headers.copy()
        headers['user-agent'] = random.choice(self.user_agents)
        return headers

    def send_request(self, url, params, cookies, timeout=15, max_retries=3):
        """
        发送HTTP请求，带有重试机制和更好的错误处理

        Args:
            url: 目标URL
            params: URL参数
            cookies: 请求使用的Cookies
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数

        Returns:
            Response对象，失败则返回None
        """
        headers = self.get_headers()

        for attempt in range(max_retries):
            try:
                # 添加随机延迟避免请求过于频繁
                if attempt > 0:
                    import time
                    delay = random.uniform(2, 5)
                    time.sleep(delay)

                # proxy_ip_port = self.proxy_manager.get_random_proxy()
                proxy_ip_port = None
                if proxy_ip_port:
                    proxy_server = {
                        "http": f"http://{proxy_ip_port}",
                        "https": f"http://{proxy_ip_port}"
                    }
                    print(f"[线程 {self.thread_id}] 使用代理: {proxy_ip_port}")
                else:
                    proxy_server = None

                res = requests.get(
                    url,
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=timeout,
                    proxies=proxy_server,
                    allow_redirects=True
                )

                if res.status_code == 200:
                    return res
                elif res.status_code == 429:
                    print(f"[线程 {self.thread_id}] 请求过于频繁，等待重试...")
                    self.update_log.emit(f"[线程 {self.thread_id}] 请求过于频繁，等待重试...")
                    import time
                    time.sleep(random.uniform(10, 20))
                else:
                    print(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}\n")
                    self.update_log.emit(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}\n")

            except requests.exceptions.Timeout:
                print(f"[线程 {self.thread_id}] 请求超时，重试 {attempt + 1}/{max_retries}")
                self.update_log.emit(f"[线程 {self.thread_id}] 请求超时，重试 {attempt + 1}/{max_retries}")
            except Exception as e:
                print(f'[线程 {self.thread_id}] {url} 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次\n')
                self.update_log.emit(
                    f"[线程 {self.thread_id}] {url} 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次\n")

        print(f"[线程 {self.thread_id}] 请求 {url} 失败，已重试 {max_retries} 次\n")
        self.update_log.emit(f"[线程 {self.thread_id}] 请求 {url} 失败，已重试 {max_retries} 次\n")
        return None

    def check_cookies_valid(self, page, response_text=""):
        """
        检查cookies是否有效，通过多种方式验证
        """
        try:
            # 方法1: 检查是否有搜索结果
            search_results = page.xpath('//div[@class="g"] | //div[contains(@class, "MjjYud")] | //h3')
            if len(search_results) >= 1:
                return True

            # 方法2: 检查是否被重定向到验证页面
            if any(keyword in response_text.lower() for keyword in [
                'sorry', 'captcha', 'unusual traffic', 'verify', 'robot', 'automated'
            ]):
                print(f"[线程 {self.thread_id}] 检测到验证页面或IP限制")
                return False

            # 方法3: 检查是否有正常的Google搜索页面元素
            google_elements = page.xpath('//div[@id="search"] | //div[@id="main"] | //form[@role="search"]')
            if len(google_elements) >= 1:
                return True

            # 方法4: 检查页面标题
            title_elements = page.xpath('//title/text()')
            if title_elements:
                title = title_elements[0].lower()
                if 'google' in title and 'search' in title:
                    return True

            return False

        except Exception as e:
            print(f"[线程 {self.thread_id}] Cookie验证出错: {e}")
            return False

    def is_blocked_or_captcha(self, response):
        """
        检查是否被阻止或出现验证码
        """
        if not response:
            return True

        # 检查URL是否被重定向
        if 'sorry' in response.url.lower():
            return True

        # 检查响应内容
        blocked_keywords = [
            'captcha', 'unusual traffic', 'verify you are human',
            'robot', 'automated', 'blocked', 'suspicious activity'
        ]

        response_text = response.text.lower()
        for keyword in blocked_keywords:
            if keyword in response_text:
                return True

        return False

    def extract_page_count(self, html_content):
        """
        从HTML内容中提取页数，使用多种方法
        """
        try:
            page = etree.HTML(html_content)

            # 方法1: 原有的XPath选择器
            try:
                pages_elem = page.xpath('//td[@class="NKTSme"][last()]/a/text()')
                if pages_elem:
                    return int(''.join(pages_elem))
            except:
                pass

            # 方法2: 查找分页导航中的数字链接
            try:
                import re
                # 查找所有包含start参数的链接
                pagination_links = page.xpath('//a[contains(@href, "start=")]')
                max_page = 1
                for link in pagination_links:
                    href = link.get('href', '')
                    text = link.text_content().strip()

                    # 从链接文本中提取数字
                    if text.isdigit():
                        max_page = max(max_page, int(text))

                    # 从href中提取start参数计算页数
                    start_match = re.search(r'start=(\d+)', href)
                    if start_match:
                        start_num = int(start_match.group(1))
                        page_num = (start_num // 10) + 1
                        max_page = max(max_page, page_num)

                if max_page > 1:
                    return min(max_page, 10)  # 限制最大10页
            except:
                pass

            # 方法3: 查找aria-label包含Page的链接
            try:
                import re
                page_links = page.xpath('//a[contains(@aria-label, "Page")]')
                max_page = 1
                for link in page_links:
                    aria_label = link.get('aria-label', '')
                    # 提取"Page X"中的数字
                    page_match = re.search(r'Page (\d+)', aria_label)
                    if page_match:
                        page_num = int(page_match.group(1))
                        max_page = max(max_page, page_num)

                if max_page > 1:
                    return min(max_page, 10)
            except:
                pass

            # 方法4: 查找导航区域的数字
            try:
                nav_numbers = page.xpath('//nav//a//text() | //div[@role="navigation"]//a//text()')
                max_page = 1
                for text in nav_numbers:
                    text = text.strip()
                    if text.isdigit() and 1 <= int(text) <= 10:
                        max_page = max(max_page, int(text))

                if max_page > 1:
                    return max_page
            except:
                pass

            # 如果所有方法都失败，检查是否有搜索结果
            results = page.xpath('//div[@class="g"] | //div[contains(@class, "MjjYud")]')
            if results:
                return 1  # 至少有1页结果
            else:
                return 0  # 没有结果

        except Exception as e:
            print(f"[线程 {self.thread_id}] 解析页面出错: {e}")
            return 1  # 默认返回1页

    def search(self, keyword, max_cookie_retries=3):
        # 更新搜索参数
        params = {
            'q': keyword,
            'hl': 'zh-CN',
            'lr': 'lang_zh-CN|lang_en',
            'num': 10,
            'start': 0
        }

        for attempt in range(max_cookie_retries):
            cookies = self.google_cookie.cookies

            response = self.send_request(
                url='https://www.google.com/search',
                params=params,
                cookies=cookies
            )

            if not response:
                print(f"[线程 {self.thread_id}] 第 {attempt + 1}/{max_cookie_retries} 次尝试失败，response=None")
                self.update_log.emit(f"[线程 {self.thread_id}] 第 {attempt + 1}/{max_cookie_retries} 次尝试失败，response=None")

                # 获取新的cookies
                with self.lock:
                    print(f"[线程 {self.thread_id}] 正在获取新的cookies...")
                    self.update_log.emit(f"[线程 {self.thread_id}] 正在获取新的cookies...")
                    self.google_cookie.cookies = self.google_cookie.create_cookies()
                continue

            # 检查是否被阻止或出现验证码
            if self.is_blocked_or_captcha(response):
                print(f"[线程 {self.thread_id}] 检测到验证码或IP被限制，等待后重试")
                self.update_log.emit(f"[线程 {self.thread_id}] 检测到验证码或IP被限制，等待后重试")

                # 长时间等待
                import time
                time.sleep(random.uniform(30, 60))

                # 获取新的cookies
                with self.lock:
                    self.google_cookie.cookies = self.google_cookie.create_cookies()
                continue

            # 解析页面并验证cookies
            page = etree.HTML(response.text)

            # 验证cookies是否有效
            if not self.check_cookies_valid(page, response.text):
                print(f"[线程 {self.thread_id}] 第 {attempt + 1}/{max_cookie_retries} 次尝试中cookies无效")
                self.update_log.emit(f"[线程 {self.thread_id}] 第 {attempt + 1}/{max_cookie_retries} 次尝试中cookies无效")

                # 获取新的cookies
                with self.lock:
                    print(f"[线程 {self.thread_id}] 正在重新获取cookies...")
                    self.update_log.emit(f"[线程 {self.thread_id}] 正在重新获取cookies...")
                    self.google_cookie.cookies = self.google_cookie.create_cookies()
                continue

            # 使用新的页数提取方法
            pages = self.extract_page_count(response.text)

            print(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}')
            self.update_log.emit(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}')
            return pages

        print(f"[线程 {self.thread_id}] 所有 {max_cookie_retries} 次尝试均失败")
        self.update_log.emit(f"[线程 {self.thread_id}] 所有 {max_cookie_retries} 次尝试均失败")
        return None


class GoogleSearchThreadManager:
    def __init__(self, num_threads=5, excel_file=None, update_log=None, browser_path=None):
        """
        初始化多线程搜索管理器

        Args:
            num_threads: 线程数
            excel_file: Excel文件路径，包含查询语句，同时也是结果文件
        """
        self.num_threads = num_threads
        self.excel_file = excel_file
        self.update_log = update_log
        self.result_lock = threading.Lock()
        self.threads = []
        self.thread_queries = []  # 每个线程负责的查询列表
        self.browser_path = browser_path

        # 进度跟踪
        self.total_queries = 0
        self.completed_queries = 0
        self.progress_callback = None

        # 初始化结果文件
        self.initialize_excel_file()

    def initialize_excel_file(self):
        """初始化Excel文件，确保有pages列"""
        if not self.excel_file or not os.path.exists(self.excel_file):
            raise FileNotFoundError(f"Excel文件不存在: {self.excel_file}")

        try:
            # 读取Excel文件
            self.results_df = pd.read_excel(self.excel_file)

            # 检查是否有pages列，如果没有则添加
            if 'pages' not in self.results_df.columns:
                self.results_df['pages'] = None
                # 保存回文件
                self.results_df.to_excel(self.excel_file, index=False)
                print(f"在Excel文件中添加了pages列")

            print(f"成功加载Excel文件: {self.excel_file}")

        except Exception as e:
            print(f"初始化Excel文件失败: {e}")
            raise

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def update_progress(self, thread_id, completed):
        """更新并报告当前进度"""
        with self.result_lock:
            self.completed_queries += completed
            if self.progress_callback and self.total_queries > 0:
                progress = (self.completed_queries / self.total_queries) * 100
                self.progress_callback(self.completed_queries, self.total_queries, progress)
            print(f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")
            self.update_log.emit(
                f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")

    def distribute_queries(self):
        """将查询分配给各个线程"""
        try:
            # 重新读取Excel文件，确保获取最新状态
            self.results_df = pd.read_excel(self.excel_file)
            print("总共有", len(self.results_df), "条数据")
            self.update_log.emit("总共有" + str(len(self.results_df)) + "条数据")
            # 获取所有未处理的查询
            unprocessed_queries = []
            for index, row in self.results_df.iterrows():
                query = row['query']
                pages = row['pages']
                if pd.isna(pages):  # 只添加未处理的查询
                    unprocessed_queries.append((index, query))

            self.total_queries = len(unprocessed_queries)
            print(f"共有 {self.total_queries} 个查询需要处理")
            self.update_log.emit(f"共有 {self.total_queries} 个查询需要处理")

            if self.total_queries == 0:
                return []

            # 将查询平均分配给各个线程
            self.thread_queries = [[] for _ in range(self.num_threads)]
            for i, (index, query) in enumerate(unprocessed_queries):
                thread_idx = i % self.num_threads
                self.thread_queries[thread_idx].append((index, query))

            # 打印每个线程分配的查询数量
            for i, queries in enumerate(self.thread_queries):
                print(f"线程 {i} 分配了 {len(queries)} 个查询")
                self.update_log.emit(f"线程 {i} 分配了 {len(queries)} 个查询")

            return self.thread_queries
        except Exception as e:
            print(f"分配查询失败: {e}")
            self.update_log.emit(f"分配查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def worker(self, thread_id, queries):
        """
        工作线程函数

        Args:
            thread_id: 线程ID
            queries: 该线程负责处理的查询列表，每个元素是(index, query)元组
        """
        if not queries:
            print(f"[线程 {thread_id}] 没有分配到查询任务")
            self.update_log.emit(f"[线程 {thread_id}] 没有分配到查询任务")
            return

        # 为每个线程创建一个GoogleSearch实例，使用唯一的cookie文件
        searcher = GoogleSearch(thread_id=thread_id, proxy_manager=ProxyManager(num=500), update_log=self.update_log)

        completed = 0
        local_results = {}  # 本地存储结果，减少文件访问 {index: pages}

        for index, query in queries:
            try:
                search_query = f'"{query}"'

                # 执行搜索
                pages = searcher.search(search_query)

                # 存储结果到本地字典
                local_results[index] = pages
                completed += 1

                # 每处理5个查询更新一次结果文件
                if completed % 5 == 0 or completed == len(queries):
                    self.update_results_file(local_results, thread_id)
                    local_results = {}  # 清空本地结果
                    self.update_progress(thread_id, 5 if completed % 5 == 0 else completed % 5)

                # 添加随机延迟，避免请求过于频繁
                # time.sleep(random.uniform(1, 2))

            except Exception as e:
                print(f"[线程 {thread_id}] 处理查询 {query} 时出错: {e}\n")
                self.update_log.emit(f"[线程 {thread_id}] 处理查询 {query} 时出错: {e}\n")

        # 处理剩余的结果
        if local_results:
            self.update_results_file(local_results, thread_id)
            self.update_progress(thread_id, len(local_results))

        print(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")
        self.update_log.emit(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")

    def update_results_file(self, results_dict, thread_id):
        """
        更新结果文件中的特定查询结果

        Args:
            results_dict: 查询结果字典 {index: pages}
            thread_id: 线程ID
        """
        if not results_dict:
            return

        try:
            # 使用线程锁保护文件读写
            with self.result_lock:
                # 重新读取最新的Excel文件
                latest_df = pd.read_excel(self.excel_file)

                # 更新结果
                for index, pages in results_dict.items():
                    # 直接使用索引更新
                    latest_df.loc[index, 'pages'] = pages

                # 保存更新后的文件
                latest_df.to_excel(self.excel_file, index=False)

                # 更新内存中的DataFrame
                self.results_df = latest_df

            print(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到文件")
            self.update_log.emit(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到文件")
        except Exception as e:
            print(f"[线程 {thread_id}] 更新结果文件失败: {e}")
            self.update_log.emit(f"[线程 {thread_id}] 更新结果文件失败: {e}")
            import traceback
            traceback.print_exc()

    def start(self):
        """启动所有工作线程"""
        # 分配查询任务给各个线程
        thread_queries = self.distribute_queries()

        if not any(thread_queries):
            print("没有需要处理的新查询")
            return False

        # 创建并启动工作线程
        for i in range(self.num_threads):
            if i < len(thread_queries) and thread_queries[i]:
                thread = threading.Thread(target=self.worker, args=(i, thread_queries[i]))
                thread.daemon = True  # 设置为守护线程
                self.threads.append(thread)
                thread.start()
                print(f"启动线程 {i}")
                self.update_log.emit(f"启动线程 {i}")

        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        print("所有线程已完成!")
        self.update_log.emit("所有线程已完成!")

    def get_results(self):
        """获取搜索结果DataFrame"""
        # 重新读取Excel文件，确保获取最新结果
        try:
            return pd.read_excel(self.excel_file)
        except Exception as e:
            print(f"读取结果文件失败: {e}")
            return self.results_df


if __name__ == '__main__':
    # 配置参数（可以在这里直接修改，不需要命令行）
    excel_file = "../search_results_email.xlsx"  # Excel文件路径
    threads = 1  # 线程数量

    print(f"===== 谷歌搜索多线程工具 =====")
    print(f"Excel文件: {excel_file}")
    print(f"线程数量: {threads}")
    print("=" * 30)

    try:
        # 创建搜索管理器
        manager = GoogleSearchThreadManager(
            num_threads=threads,
            excel_file=excel_file
        )

        # 启动搜索
        print("开始分配查询任务...")
        started = manager.start()

        # 如果有任务需要执行
        if started:
            print("正在执行查询，请稍候...")
            manager.wait_completion()
            print("所有查询已完成!")
        else:
            print("没有需要处理的查询")

        print(f"\n处理完成: 共处理了 {manager.completed_queries} 个查询")
        print(f"结果已保存到 {excel_file}")

    except KeyboardInterrupt:
        print("\n程序被用户中断，当前结果已保存")
        print(f"您可以稍后重新运行程序继续执行")

    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback

        traceback.print_exc()

    # 程序结束前等待用户按键，方便查看输出
    input("\n按回车键退出...")
