# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON> Time: 2025/3/9 13:05
"""
import asyncio
import random
import logging
import json
import os
from playwright.async_api import async_playwright
from email_page.proxy import ProxyManager

if not os.path.exists('../cookies'):
    os.makedirs('../cookies')


class HumanBehaviorSimulator:
    """人类行为模拟工具类"""

    @staticmethod
    def get_random_viewport():
        """生成随机设备视口"""
        return {
            "width": random.choice([1920, 1366, 1536, 1440]),
            "height": random.choice([1080, 768, 864, 900])
        }

    @staticmethod
    async def simulate_typing(element, text):
        """模拟人类输入行为"""
        for char in text:
            await element.type(char)
            await asyncio.sleep(random.uniform(0.08, 0.15))
            if random.random() < 0.07:
                await element.press("Backspace")
                await element.type(char)

    @staticmethod
    async def random_mouse_movement(page):
        """生成随机鼠标轨迹"""
        for _ in range(random.randint(3, 5)):
            x = random.randint(0, page.viewport_size['width'])
            y = random.randint(0, page.viewport_size['height'])
            await page.mouse.move(x, y, steps=random.randint(2, 5))
            await asyncio.sleep(random.uniform(0.3, 0.7))

    @staticmethod
    async def simulate_scroll(page):
        """模拟自然滚动"""
        scroll_distance = random.randint(300, 800)
        await page.mouse.wheel(0, scroll_distance)
        await asyncio.sleep(random.uniform(0.5, 1.2))

    @staticmethod
    async def random_page_interaction(page):
        """随机页面交互"""
        actions = [
            lambda: page.mouse.wheel(0, random.randint(200, 500)),
            lambda: HumanBehaviorSimulator.random_mouse_movement(page),
            lambda: page.keyboard.press('PageDown'),
            lambda: asyncio.sleep(random.uniform(0.5, 1.5))
        ]
        for _ in range(random.randint(2, 4)):
            await random.choice(actions)()
            await asyncio.sleep(random.uniform(0.3, 0.8))


class GoogleCookie:
    """谷歌Cookie类，合并了管理和获取功能"""

    def __init__(self, cookie_file="google_cookies.json",):
        self.cookies = {}
        self.cookie_file = cookie_file
        self.human_simulator = HumanBehaviorSimulator()
        self.config = {
            'timeout': 30000,
            'headless': True
        }
        self.load_cookies_from_file()

    def load_cookies_from_file(self):
        """从文件加载cookies"""
        if os.path.exists(self.cookie_file):
            try:
                with open(self.cookie_file, 'r') as f:
                    self.cookies = json.load(f)
                if self.cookies == {}:
                    self.create_cookies()
                logging.info("Cookies loaded from file")
            except:
                logging.warning("Failed to load cookies from file")
                self.cookies = {}

    def save_cookies_to_file(self):
        """保存cookies到文件"""
        with open(self.cookie_file, 'w') as f:
            json.dump(self.cookies, f)
        logging.info("Cookies saved to file")

    def get_cookies(self):
        """获取cookies"""
        return self.cookies

    def update_cookies(self, new_cookies):
        """更新cookies"""
        self.cookies = new_cookies
        self.save_cookies_to_file()

    def is_cookies_valid(self):
        """检查cookies是否有效"""
        if not self.cookies:
            return False

        # 检查cookie是否包含必要的字段
        # Google搜索常见的重要cookies
        important_cookies = ['AEC', 'NID', 'SOCS']  # 添加SOCS cookie

        # 至少要有一个重要的cookie
        has_important_cookie = any(cookie in self.cookies for cookie in important_cookies)
        if not has_important_cookie:
            return False

        # 检查cookies是否过期（简单检查）
        # 如果cookies太少，可能无效
        if len(self.cookies) < 2:
            return False

        return True

    async def _detect_captcha(self, page):
        """检测是否出现验证码"""
        captcha_selectors = [
            'form#captcha-form',
            'div.g-recaptcha',
            'input[name="captcha"]',
            'title:has-text("unusual traffic")'
        ]

        for selector in captcha_selectors:
            if await page.query_selector(selector):
                logging.warning("检测到验证码!")
                return True
        return False

    async def _perform_search(self, page, query):
        """执行搜索操作"""
        logging.info(f"正在执行搜索操作: {query}")
        await page.goto('https://www.google.com', timeout=self.config.get('timeout', 30000))
        await page.wait_for_load_state('networkidle')

        search_box = await page.wait_for_selector('textarea[name="q"]', timeout=15000)
        await HumanBehaviorSimulator.simulate_typing(search_box, query)
        await asyncio.sleep(random.uniform(0.8, 1.3))
        await search_box.press('Enter')

        await page.wait_for_selector('div#search', timeout=20000)
        return not await self._detect_captcha(page)

    async def fetch_new_cookies(self, search_query="python programming"):
        """获取谷歌搜索页面的cookies"""
        async with async_playwright() as p:
            # 获取代理
            proxy_manager = ProxyManager(num=2, minute=5)
            proxy = proxy_manager.get_random_proxy()  # ip:port
            # proxy=None
            if proxy and ':' in proxy:
                proxy_server = {'server': f'http://{proxy}'}
            else:
                proxy_server = None
            # 随机选择一个浏览器UA
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ]
            # 浏览器启动选项
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--no-sandbox',
                '--disable-setuid-sandbox',
            ]

            # 浏览器配置
            browser_options = {
                'headless': self.config.get('headless', True),
                'args': browser_args,
                "ignore_default_args": ['--enable-automation']
            }
            print(f"使用代理服务器: {proxy_server}  获取cookies")
            browser = await p.chromium.launch(**browser_options, proxy=proxy_server)

            # 创建上下文
            viewport = self.human_simulator.get_random_viewport()
            context = await browser.new_context(
                viewport=viewport,
                user_agent=random.choice(user_agents)
            )

            # 禁用WebDriver
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // 隐藏自动化特征
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({state: Notification.permission}) :
                        originalQuery(parameters)
                );

                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5].map(() => ({
                        0: {
                            type: "application/x-google-chrome-pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format"
                        },
                        name: "Chrome PDF Plugin",
                        filename: "internal-pdf-viewer",
                        description: "Portable Document Format",
                        length: 1
                    }))
                });
            """)

            # 创建新页面
            page = await context.new_page()

            try:
                # 执行搜索操作
                search_success = await self._perform_search(page, search_query)

                if not search_success:
                    logging.warning("搜索操作失败，可能被检测为机器人")
                    return {}

                # 模拟随机互动
                await self.human_simulator.random_page_interaction(page)

                # 获取cookies
                cookies = await context.cookies()
                formatted_cookies = {}

                # 格式化cookies为字典
                for cookie in cookies:
                    if cookie.get('domain', '').endswith('google.com'):
                        formatted_cookies[cookie['name']] = cookie['value']

                # 更新cookie
                print(f"获取新的cookies: {formatted_cookies}")
                self.update_cookies(formatted_cookies)
                logging.info("Cookies fetched successfully!")

                # 返回cookies
                return formatted_cookies

            except Exception as e:
                logging.error(f"Error fetching cookies: {e}")
                return {}

            finally:
                await browser.close()

    def create_cookies(self):
        """获取有效的cookies，如果无效则重新获取"""
        # 随机生成搜索词，使获取cookie的过程更自然
        search_queries = [
            "what is LLM",
            "news headlines",
            "best restaurants near me",
            "how to learn python",
            "upcoming movies",
            "stock market news",
            "healthy recipes",
            "travel destinations",
            "latest technology trends",
            "home workout routines",
            "best restaurants in town"
        ]

        # 获取新的cookies
        cookies = asyncio.run(self.fetch_new_cookies(random.choice(search_queries)))
        return cookies


async def main():
    cookie_handler = GoogleCookie()

    # 获取cookies，如果现有cookie无效则自动获取新cookie
    cookies = cookie_handler.get_cookies()
    if not cookie_handler.is_cookies_valid():
        print("现有cookie无效，正在获取新cookie...")
        cookies = await cookie_handler.fetch_new_cookies()

    print("当前cookie:", cookies)


if __name__ == "__main__":
    asyncio.run(main())
