#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML获取质量的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_page.spider import GoogleSearch

def test_html_quality():
    """测试HTML获取质量"""
    print("=== 测试HTML获取质量 ===")
    
    # 创建搜索实例
    searcher = GoogleSearch(thread_id=999)  # 使用特殊ID便于识别
    
    # 测试关键词
    test_keywords = [
        'python programming',
        'machine learning tutorial',
        'web development guide'
    ]
    
    for keyword in test_keywords:
        print(f"\n--- 测试关键词: {keyword} ---")
        
        try:
            # 执行搜索，会自动保存HTML到debug_html目录
            pages = searcher.search(keyword)
            print(f"提取到的页数: {pages}")
            
            # 检查保存的HTML文件
            debug_dir = "debug_html"
            if os.path.exists(debug_dir):
                files = [f for f in os.listdir(debug_dir) if f.startswith(f"google_search_999_")]
                if files:
                    latest_file = max(files, key=lambda x: os.path.getctime(os.path.join(debug_dir, x)))
                    file_path = os.path.join(debug_dir, latest_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    
                    print(f"HTML文件: {latest_file}")
                    print(f"HTML大小: {len(html_content)} 字符")
                    
                    # 分析HTML内容质量
                    analyze_html_quality(html_content, keyword)
            
        except Exception as e:
            print(f"测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 50)

def analyze_html_quality(html_content, keyword):
    """分析HTML内容质量"""
    print("\n📊 HTML质量分析:")
    
    # 基本检查
    checks = {
        "包含关键词": keyword.lower() in html_content.lower(),
        "包含Google标识": "google" in html_content.lower(),
        "包含搜索结果": any(cls in html_content for cls in ["class=\"g\"", "class=\"MjjYud\"", "<h3"]),
        "包含分页元素": "NKTSme" in html_content,
        "包含导航元素": any(nav in html_content for nav in ["<nav", "role=\"navigation\"", "aria-label=\"Page\""]),
        "不是错误页面": not any(err in html_content.lower() for err in ["sorry", "error", "captcha", "unusual traffic"]),
        "HTML结构完整": html_content.count("<html") > 0 and html_content.count("</html>") > 0,
        "包含CSS样式": "<style" in html_content or "stylesheet" in html_content,
        "包含JavaScript": "<script" in html_content,
        "内容充足": len(html_content) > 50000  # 正常Google搜索页面应该很大
    }
    
    for check, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check}")
    
    # 统计通过率
    passed = sum(checks.values())
    total = len(checks)
    score = (passed / total) * 100
    
    print(f"\n📈 质量评分: {score:.1f}% ({passed}/{total})")
    
    if score >= 80:
        print("🎉 HTML质量优秀，应该能正确提取页数")
    elif score >= 60:
        print("⚠️ HTML质量一般，可能需要进一步优化")
    else:
        print("🚨 HTML质量较差，需要改进请求策略")
    
    # 详细分析
    print(f"\n📋 详细信息:")
    print(f"  - HTML大小: {len(html_content):,} 字符")
    print(f"  - 包含<div>标签: {html_content.count('<div')} 个")
    print(f"  - 包含<a>链接: {html_content.count('<a')} 个")
    print(f"  - 包含class属性: {html_content.count('class=')} 个")
    
    # 查找分页相关元素
    pagination_indicators = [
        ("NKTSme类", html_content.count('class="NKTSme"')),
        ("分页链接", html_content.count('start=')),
        ("Page标签", html_content.count('Page ')),
        ("导航元素", html_content.count('<nav')),
        ("aria-label", html_content.count('aria-label'))
    ]
    
    print(f"\n🔍 分页元素统计:")
    for name, count in pagination_indicators:
        print(f"  - {name}: {count} 个")

def main():
    """主函数"""
    print("Google搜索HTML质量测试工具")
    print("=" * 60)
    print("此工具将:")
    print("1. 测试不同关键词的搜索")
    print("2. 保存HTML到debug_html目录")
    print("3. 分析HTML内容质量")
    print("4. 评估页数提取的可能性")
    print("=" * 60)
    
    test_html_quality()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("请检查debug_html目录中的HTML文件")
    print("如果质量评分较低，说明需要进一步优化请求策略")

if __name__ == "__main__":
    main()
